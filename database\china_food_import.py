#!/usr/bin/env python3
"""
中国食物成分表数据导入脚本
从GitHub项目下载并导入中国食物成分表数据
"""

import os
import json
import requests
import mysql.connector
from mysql.connector import Error
import logging
from pathlib import Path
import zipfile
import tempfile
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChinaFoodImporter:
    def __init__(self, host='localhost', database='meals', user='root', password='021026'):
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
        
        # GitHub项目信息
        self.github_repo = "https://github.com/Sanotsu/china-food-composition-data"
        self.download_url = "https://github.com/Sanotsu/china-food-composition-data/archive/refs/heads/main.zip"

    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                use_unicode=True,
                autocommit=False
            )
            
            if self.connection.is_connected():
                logger.info(f"成功连接到MySQL数据库: {self.database}")
                return True
                
        except Error as e:
            logger.error(f"连接MySQL失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("MySQL连接已关闭")

    def download_and_extract_data(self):
        """下载并解压中国食物数据"""
        logger.info("开始下载中国食物成分表数据...")
        
        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            zip_path = os.path.join(temp_dir, "china_food_data.zip")
            
            # 下载ZIP文件
            response = requests.get(self.download_url, stream=True)
            response.raise_for_status()
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info("下载完成，开始解压...")
            
            # 解压文件
            extract_dir = os.path.join(temp_dir, "extracted")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # 查找JSON数据目录
            data_dirs = []
            for root, dirs, files in os.walk(extract_dir):
                if 'json_data_vision' in dirs:
                    data_dirs.append(os.path.join(root, 'json_data_vision'))
                if 'json_gi_of_foods' in dirs:
                    data_dirs.append(os.path.join(root, 'json_gi_of_foods'))
            
            logger.info(f"找到数据目录: {data_dirs}")
            return data_dirs, temp_dir
            
        except Exception as e:
            logger.error(f"下载数据失败: {e}")
            return None, None

    def execute_sql_file(self, sql_file_path):
        """执行SQL文件"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # 分割SQL语句
            sql_commands = sql_script.split(';')
            cursor = self.connection.cursor()
            
            for command in sql_commands:
                command = command.strip()
                if command:
                    try:
                        cursor.execute(command)
                    except Error as e:
                        logger.warning(f"执行SQL命令时出现警告: {e}")
            
            self.connection.commit()
            cursor.close()
            logger.info(f"SQL文件执行完成: {sql_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"执行SQL文件失败: {e}")
            return False

    def clean_value(self, value):
        """清理数据值"""
        if value is None or value == '' or value == 'Tr' or value == '-':
            return None
        
        try:
            # 尝试转换为数字
            if isinstance(value, str):
                value = value.strip()
                if value.replace('.', '').replace('-', '').isdigit():
                    return float(value) if '.' in value else int(value)
            return value
        except:
            return None

    def import_china_foods_from_json(self, json_file_path):
        """从JSON文件导入中国食物数据"""
        try:
            logger.info(f"开始导入JSON文件: {json_file_path}")
            
            with open(json_file_path, 'r', encoding='utf-8') as f:
                foods_data = json.load(f)
            
            cursor = self.connection.cursor()
            
            # 准备插入语句
            insert_query = """
            INSERT IGNORE INTO china_foods (
                food_code, food_name, edible, water, energy_kcal, energy_kj,
                protein, fat, cho, dietary_fiber, cholesterol, ash,
                vitamin_a, carotene, retinol, thiamin, riboflavin, niacin,
                vitamin_c, vitamin_e_total, vitamin_e1, vitamin_e2, vitamin_e3,
                ca, p, k, na, mg, fe, zn, se, cu, mn, remark
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            imported_count = 0
            for food in foods_data:
                try:
                    values = (
                        food.get('foodCode'),
                        food.get('foodName'),
                        self.clean_value(food.get('edible')),
                        self.clean_value(food.get('water')),
                        self.clean_value(food.get('energyKCal')),
                        self.clean_value(food.get('energyKJ')),
                        self.clean_value(food.get('protein')),
                        self.clean_value(food.get('fat')),
                        self.clean_value(food.get('CHO')),
                        self.clean_value(food.get('dietaryFiber')),
                        self.clean_value(food.get('cholesterol')),
                        self.clean_value(food.get('ash')),
                        self.clean_value(food.get('vitaminA')),
                        self.clean_value(food.get('carotene')),
                        self.clean_value(food.get('retinol')),
                        self.clean_value(food.get('thiamin')),
                        self.clean_value(food.get('riboflavin')),
                        self.clean_value(food.get('niacin')),
                        self.clean_value(food.get('vitaminC')),
                        self.clean_value(food.get('vitaminETotal')),
                        self.clean_value(food.get('vitaminE1')),
                        self.clean_value(food.get('vitaminE2')),
                        self.clean_value(food.get('vitaminE3')),
                        self.clean_value(food.get('Ca')),
                        self.clean_value(food.get('P')),
                        self.clean_value(food.get('K')),
                        self.clean_value(food.get('Na')),
                        self.clean_value(food.get('Mg')),
                        self.clean_value(food.get('Fe')),
                        self.clean_value(food.get('Zn')),
                        self.clean_value(food.get('Se')),
                        self.clean_value(food.get('Cu')),
                        self.clean_value(food.get('Mn')),
                        food.get('remark', '')
                    )
                    
                    cursor.execute(insert_query, values)
                    imported_count += 1
                    
                except Exception as e:
                    logger.warning(f"导入食物数据失败: {food.get('foodName', 'Unknown')} - {e}")
            
            self.connection.commit()
            cursor.close()
            logger.info(f"JSON文件导入完成: {json_file_path}，成功导入 {imported_count} 条记录")
            return imported_count
            
        except Exception as e:
            logger.error(f"导入JSON文件失败: {e}")
            return 0

    def import_all_data(self):
        """导入所有中国食物数据"""
        # 下载数据
        data_dirs, temp_dir = self.download_and_extract_data()
        if not data_dirs:
            return False
        
        try:
            # 执行数据库架构
            schema_file = Path("database/china_food_schema.sql")
            if not schema_file.exists():
                schema_file = Path("china_food_schema.sql")
            
            if schema_file.exists():
                logger.info("创建中国食物数据库架构...")
                self.execute_sql_file(schema_file)
            
            total_imported = 0
            
            # 导入JSON数据
            for data_dir in data_dirs:
                if os.path.exists(data_dir):
                    for json_file in Path(data_dir).glob("*.json"):
                        count = self.import_china_foods_from_json(json_file)
                        total_imported += count
            
            logger.info(f"所有数据导入完成，总计导入 {total_imported} 条记录")
            return True
            
        finally:
            # 清理临时文件
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info("临时文件已清理")

def main():
    """主函数"""
    print("中国食物成分表数据导入工具")
    print("=" * 50)
    
    # 数据库连接配置
    db_config = {
        'host': input("MySQL主机 (默认: localhost): ").strip() or 'localhost',
        'user': input("MySQL用户名 (默认: root): ").strip() or 'root',
        'password': input("MySQL密码: ").strip(),
        'database': input("数据库名 (默认: meals): ").strip() or 'meals'
    }
    
    # 创建导入器
    importer = ChinaFoodImporter(**db_config)
    
    # 连接数据库
    if not importer.connect():
        return
    
    try:
        # 导入数据
        success = importer.import_all_data()
        
        if success:
            print("\n中国食物成分表数据导入完成！")
        else:
            print("数据导入失败")
    
    finally:
        importer.disconnect()

if __name__ == "__main__":
    main()
